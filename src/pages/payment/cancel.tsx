import { useNavigate } from "react-router-dom";
import logo from "@/assets/adeptos_logo.png";

export function PaymentCancelPage() {
  const navigate = useNavigate();

  const handleRetryPayment = () => {
    navigate("/offer");
  };

  const handleReturnHome = () => {
    navigate("/");
  };

  const handleGetFreeAudit = () => {
    navigate("/app");
  };

  return (
    <div className="relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
      <main className="relative z-10 px-4 sm:px-6 lg:px-8 py-16 min-h-screen flex items-center justify-center">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <img
              src={logo}
              alt="Adeptos Logo"
              className="h-16 sm:h-20 mx-auto mb-6"
            />
          </div>
          
          <div className="space-y-8">
            {/* Cancel Icon */}
            <div className="w-20 h-20 mx-auto bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
              <svg
                className="w-10 h-10 text-orange-600 dark:text-orange-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>

            {/* Cancel Message */}
            <div className="space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-title font-bold text-dark dark:text-white theme-transition">
                Payment Cancelled
              </h1>
              <p className="text-lg sm:text-xl font-body text-dark dark:text-white theme-transition">
                Your payment was cancelled or could not be processed. No charges have been made to your account.
              </p>
            </div>

            {/* Information Box */}
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6 text-left">
              <h3 className="text-lg font-title font-semibold text-orange-800 dark:text-orange-200 mb-3">
                What happened?
              </h3>
              <ul className="space-y-2 text-orange-700 dark:text-orange-300 font-body">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  You may have clicked the back button or closed the payment window
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  There might have been an issue with your payment method
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  The payment session may have expired
                </li>
              </ul>
            </div>

            {/* Next Steps */}
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 text-left">
              <h3 className="text-lg font-title font-semibold text-dark dark:text-white theme-transition mb-3">
                What can you do next?
              </h3>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300 font-body">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-dark dark:bg-light rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Try the payment process again with the same or different payment method
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-dark dark:bg-light rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Get a free AI business audit to see our value first
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-dark dark:bg-light rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Contact our support team if you continue to experience issues
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {/* Primary Action - Retry Payment */}
              <button
                onClick={handleRetryPayment}
                className="w-full px-8 py-4 text-lg font-body font-semibold bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:ring-offset-2 focus:ring-offset-transparent border-2 border-green-500"
              >
                Try Payment Again
              </button>

              {/* Secondary Actions */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleGetFreeAudit}
                  className="px-6 py-3 text-base font-body font-semibold bg-dark dark:bg-light text-white dark:text-dark rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  Get Free Audit
                </button>
                <button
                  onClick={handleReturnHome}
                  className="px-6 py-3 text-base font-body font-semibold bg-white/10 dark:bg-gray-800/30 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 rounded-full hover:bg-white/20 dark:hover:bg-gray-800/50 hover:border-dark/40 dark:hover:border-light/40 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  Return Home
                </button>
              </div>
            </div>

            {/* Support Information */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <p className="text-sm font-body text-gray-600 dark:text-gray-400">
                Need help? Contact our support team and we'll be happy to assist you with your purchase.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
