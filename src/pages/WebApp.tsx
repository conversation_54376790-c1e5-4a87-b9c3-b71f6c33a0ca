import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Phase0,
  Phase1,
  Phase2,
  Phase3,
  Phase4,
  WebAppHeader,
} from "@/components/webApp/phases";
import type { WelcomeData } from "@/components/webApp/phases/Phase0";
import type { BusinessProfileData } from "@/components/webApp/phases/Phase1";
import type { ChallengesData } from "@/components/webApp/phases/Phase2";
import { type ReCaptchaRef } from "@/components/shared/ReCaptcha";
import { postToApi } from "@/lib/api";
import type { CreateLeadPayload, CreateLeadResponse } from "@/types/payloads/leadPayloads";

interface WebAppFormData {
  phase0?: WelcomeData;
  phase1?: BusinessProfileData;
  phase2?: ChallengesData;
}
const TOTAL_PHASES = 5;

export function WebApp() {
  const [phase, setPhase] = useState(0);
  const [formData, setFormData] = useState<WebAppFormData>({});
  const [report, setReport] = useState<CreateLeadResponse | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const recaptchaRef = useRef<ReCaptchaRef>(null);

  const navigate = useNavigate();

  const handleBack = () => {
    if (phase > 0) {
      setPhase(phase - 1);
      setCaptchaToken(null);
      setSubmitError(null);
      recaptchaRef.current?.reset();
    }
  };

  const handleNext = () => {
    if (phase < TOTAL_PHASES - 1) {
      setPhase(phase + 1);
    } else {
      setPhase(TOTAL_PHASES);
    }
  };

  const handleSubmit = async () => {
    if (!captchaToken) {
      setSubmitError("Please complete the reCAPTCHA verification");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      setPhase(4);
      const createPayload: CreateLeadPayload = {
        companyName: formData.phase0?.companyName || "",
        email: formData.phase0?.email || "",
        fullName: formData.phase0?.fullName || "",
        phone: formData.phase0?.phone || "",
        website: formData.phase0?.website || "",
        businessType: formData.phase1?.businessType || "",
        employeeCount: formData.phase1?.employeeCount || "",
        industry: formData.phase1?.industry || "",
        monthlyRevenue: formData.phase1?.monthlyRevenue || "",
        salesLocation: formData.phase1?.salesLocation || "",
        bottlenecks: formData.phase2?.bottlenecks || "",
        timeConsumingTasks: formData.phase2?.timeConsumingTasks || "",
      }
      console.log("createPayload", createPayload);
      const response = await postToApi<CreateLeadResponse, CreateLeadPayload>("/leads", createPayload, false);
      console.log("response", response);
      if (response.error) {
        setSubmitError(response.error);
        return;
      }
      setReport(response.data);

    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Submission failed. Please try again.");
      recaptchaRef.current?.reset();
      setCaptchaToken(null);
    } finally {
      setIsSubmitting(false);
    }
  };

  const setPhase0Data = (data: WelcomeData) => {
    setFormData((prev) => ({ ...prev, phase0: data }));
  };

  const setPhase1Data = (data: BusinessProfileData) => {
    setFormData((prev) => ({ ...prev, phase1: data }));
  };

  const setPhase2Data = (data: ChallengesData) => {
    setFormData((prev) => ({ ...prev, phase2: data }));
  };

  const handleLoadingComplete = () => {
    navigate(`/app/report/${report?.Lead.id}`);
  };

  const renderPhase = () => {
    switch (phase) {
      case 0:
        return (
          <Phase0
            onNext={handleNext}
            formData={formData.phase0}
            setFormData={setPhase0Data}
          />
        );
      case 1:
        return (
          <Phase1
            onNext={handleNext}
            formData={formData.phase1}
            setFormData={setPhase1Data}
          />
        );
      case 2:
        return (
          <Phase2
            onNext={handleNext}
            formData={formData.phase2}
            setFormData={setPhase2Data}
          />
        );
      case 3:
        return (
          <Phase3
            recaptchaRef={recaptchaRef}
            submitError={submitError}
            isSubmitting={isSubmitting}
            captchaToken={captchaToken}
            setCaptchaToken={setCaptchaToken}
            handleFinalSubmit={handleSubmit}
          />
        );
      case 4:
        return (
          <Phase4
            fetchCompleted={report !== null}
            onComplete={handleLoadingComplete}
          />
        );
      default:
        return (
          <div className="text-center text-gray dark:text-gray">
            Phase not found
          </div>
        );
    }
  };

  return (
    <div className="web-app-container relative min-h-screen overflow-x-hidden theme-transition">
      {/* Header */}
      <WebAppHeader
        currentPhase={Math.min(phase, TOTAL_PHASES - 1)}
        totalPhases={TOTAL_PHASES}
        onBack={handleBack}
        canGoBack={phase > 0 && phase < TOTAL_PHASES}
      />

      {/* Main Content */}
      <main className="relative z-10 pt-20 sm:pt-24 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 pb-8 min-h-screen">
        <div className="max-w-4xl mx-auto">{renderPhase()}</div>
      </main>
    </div>
  );
}
