export interface ApiResponse<T = any> {
  data: T | null;
  error: string | null;
}

const BASE_API_URL = import.meta.env.VITE_API_URL;
const BASIC_HEADERS = {
  "Content-Type": "application/json",
};

async function handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
  try {
    const backendResponse = await response.json();

    if (!response.ok) {
      const errorMessage = backendResponse.error.message || `HTTP ${response.status}: ${response.statusText}`;
      return {
        data: null,
        error: errorMessage,
      };
    }
    return backendResponse;
  } catch (error) {
    return {
      data: null,
      error: "Network error occurred",
    };
  }
}

export async function getFromApi<T>(
  url: string,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "GET",
      headers,
      credentials: auth ? "include" : "omit",
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: "Network error occurred",
    };
  }
}

export async function postToApi<T, U = any>(
  url: string,
  data: U,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "POST",
      headers,
      credentials: auth ? "include" : "omit",
      body: JSON.stringify(data),
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: "Network error occurred",
    };
  }
}

