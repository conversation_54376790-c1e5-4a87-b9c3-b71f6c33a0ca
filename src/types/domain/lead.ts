import type { Report } from "@/types/domain/report";

export interface Lead {
  id: string;
  fullName: string;
  companyName: string;
  email: string;
  phone: string;
  website: string;
  businessType: string;
  employeeCount: string;
  industry: string;
  monthlyRevenue: string;
  salesLocation: string;
  bottlenecks: string;
  timeConsumingTasks: string;
  reports: Report[];
  createdAt: string;
  updatedAt: string;
}