import { useEffect, useState } from "react";

interface Phase4Props {
  onComplete: () => void;
  fetchCompleted: boolean;
  hasApiError?: boolean;
  apiErrorMessage?: string | null;
}

interface LoadingStep {
  id: number;
  text: string;
  completed: boolean;
}

export function Phase4({ onComplete, fetchCompleted, hasApiError = false, apiErrorMessage }: Phase4Props) {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const steps: LoadingStep[] = [
    { id: 1, text: "Analyzing Operational Inefficiencies...", completed: false },
    { id: 2, text: "Cross-Referencing Industry Benchmarks...", completed: false },
    { id: 3, text: "Identifying High-ROI Automation Points...", completed: false },
    { id: 4, text: "Projecting Potential Time & Cost Savings...", completed: false },
  ];

  const [loadingSteps, setLoadingSteps] = useState(steps);

  useEffect(() => {
    // If there's an API error, skip the loading animation and show completion immediately
    if (hasApiError) {
      setProgress(100);
      setIsComplete(true);
      return;
    }

    // Start the loading sequence
    const stepDuration = 2500; // 2.5 seconds per step
    const totalDuration = stepDuration * steps.length; // Total time for all steps
    const progressInterval = 50; // Update progress every 50ms
    const progressIncrement = 100 / (totalDuration / progressInterval);

    let progressTimer: NodeJS.Timeout;

    const startProgress = () => {
      let currentProgress = 0;

      progressTimer = setInterval(() => {
        currentProgress += progressIncrement;
        setProgress(Math.min(currentProgress, 100));

        if (currentProgress >= 100) {
          clearInterval(progressTimer);
          // Wait a moment then show completion
          setTimeout(() => {
            setIsComplete(true);
          }, 500);
        }
      }, progressInterval);
    };

    const completeStep = (stepIndex: number) => {
      setLoadingSteps(prev =>
        prev.map((step, index) =>
          index === stepIndex ? { ...step, completed: true } : step
        )
      );
      setCurrentStep(stepIndex + 1);
    };

    // Start the sequence
    startProgress();

    // Complete steps at intervals
    const stepTimers = steps.map((_, index) =>
      setTimeout(() => completeStep(index), (index + 1) * stepDuration)
    );

    return () => {
      clearInterval(progressTimer);
      stepTimers.forEach(timer => clearTimeout(timer));
    };
  }, [hasApiError]);

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 min-h-[calc(100dvh-8rem)] md:min-h-auto">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            {hasApiError ? 'Report Generation Complete' : 'Our AI Agent is working on creating your custom report.'}
          </h1>
        </div>

        {/* Subtitle */}
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition leading-relaxed">
            {hasApiError
              ? 'Your report is ready to view. Note: Some features may be limited due to a processing issue.'
              : 'Please do not close the page.'
            }
          </p>
          {hasApiError && apiErrorMessage && (
            <p className="text-sm text-red-600 dark:text-red-400 font-body mt-2">
              Technical details: {apiErrorMessage}
            </p>
          )}
        </div>

        {/* Progress Bar */}
        <div className="max-w-2xl mx-auto space-y-8">
          <div className="space-y-4">
            <div className="w-full bg-gray/20 dark:bg-light/20 rounded-full h-3 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-dark to-gray-700 dark:from-light dark:to-gray-300 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
            <div className="text-center">
              <span className="text-sm font-body text-gray dark:text-gray theme-transition">
                {Math.round(progress)}% Complete
              </span>
            </div>
          </div>

          {/* Loading Steps */}
          <div className="space-y-6">
            {loadingSteps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center space-x-4 transition-all duration-500 ${step.completed ? 'opacity-100' : 'opacity-70'
                  }`}
              >
                <div className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-500 ${step.completed
                  ? 'bg-dark dark:bg-light border-dark dark:border-light'
                  : 'border-gray/40 dark:border-light/40'
                  }`}>
                  {step.completed ? (
                    <svg
                      className="w-3 h-3 text-white dark:text-dark"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <div className={`w-2 h-2 rounded-full transition-all duration-500 ${index === currentStep ? 'bg-dark dark:bg-light animate-pulse' : 'bg-gray/40 dark:bg-light/40'
                      }`} />
                  )}
                </div>
                <span className={`text-base sm:text-lg font-body transition-all duration-500 ${step.completed
                  ? 'text-dark dark:text-white font-semibold'
                  : 'text-gray dark:text-gray'
                  } theme-transition`}>
                  {step.text}
                </span>
              </div>
            ))}
          </div>

          {/* Completion Button */}
          {isComplete && fetchCompleted && (
            <div className="text-center pt-8 animate-[fadeIn_0.5s_ease-in-out]">
              <button
                type="button"
                onClick={onComplete}
                className="
                  px-8 sm:px-12 md:px-16 py-4 sm:py-5
                  text-lg sm:text-xl font-body font-bold
                  bg-dark dark:bg-light
                  text-white dark:text-dark
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-xl
                  theme-transition
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-dark/50 dark:focus:ring-light/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  min-h-[56px] sm:min-h-[64px]
                  w-full max-w-md mx-auto
                  hover:bg-gray-800 dark:hover:bg-gray-200
                  animate-[bounceSubtle_2s_ease-in-out_infinite]
                "
                aria-label="View your AI audit report"
              >
                View AI Audit Report for your Business
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
