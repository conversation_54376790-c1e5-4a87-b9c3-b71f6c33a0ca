import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import logo from "@/assets/adeptos_logo.png";
import { getFromApi } from "@/lib/api";
import type { CheckoutResponse } from "@/types/payloads/stripePayloads";


export function PaymentSuccessPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [session, setSession] = useState<CheckoutResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const sessionId = searchParams.get("session_id");

  useEffect(() => {
    if (!sessionId) {
      setError("No session ID provided");
      setIsLoading(false);
      return;
    }

    fetchSession(sessionId);
  }, []);

  async function fetchSession(sessionId: string) {
    const response = await getFrom<PERSON><PERSON><CheckoutResponse>(`/payment/checkout/${sessionId}`, false);
    if (response.error) {
      setError(response.error);
      setIsLoading(false);
      return;
    }
    setSession(response.data);
    setIsLoading(false);
  }


  const handleReturnHome = () => {
    navigate("/");
  };

  const handleViewOffer = () => {
    navigate("/offer");
  };

  if (isLoading) {
    return (
      <div className="relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
        <main className="relative z-10 px-4 sm:px-6 lg:px-8 py-16 min-h-screen flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <img
                src={logo}
                alt="Adeptos Logo"
                className="h-16 sm:h-20 mx-auto mb-6"
              />
            </div>
            <div className="space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dark dark:border-light mx-auto"></div>
              <p className="text-lg font-body text-dark dark:text-white theme-transition">
                Verifying your payment...
              </p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
        <main className="relative z-10 px-4 sm:px-6 lg:px-8 py-16 min-h-screen flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <img
                src={logo}
                alt="Adeptos Logo"
                className="h-16 sm:h-20 mx-auto mb-6"
              />
            </div>

            <div className="space-y-6">
              <div className="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-red-600 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>

              <div className="space-y-4">
                <h1 className="text-3xl sm:text-4xl font-title font-bold text-dark dark:text-white theme-transition">
                  Payment Verification Failed
                </h1>
                <p className="text-lg font-body text-dark dark:text-white theme-transition">
                  {error || "Unable to verify your payment. Please contact support if you believe this is an error."}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleReturnHome}
                  className="px-6 py-3 text-base font-body font-semibold bg-dark dark:bg-light text-white dark:text-dark rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  Return Home
                </button>
                <button
                  onClick={handleViewOffer}
                  className="px-6 py-3 text-base font-body font-semibold bg-white/10 dark:bg-gray-800/30 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 rounded-full hover:bg-white/20 dark:hover:bg-gray-800/50 hover:border-dark/40 dark:hover:border-light/40 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  View Offer
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
      <main className="relative z-10 px-4 sm:px-6 lg:px-8 py-16 min-h-screen flex items-center justify-center">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <img
              src={logo}
              alt="Adeptos Logo"
              className="h-16 sm:h-20 mx-auto mb-6"
            />
          </div>

          <div className="space-y-8">
            {/* Success Icon */}
            <div className="w-20 h-20 mx-auto bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <svg
                className="w-10 h-10 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>

            {/* Success Message */}
            <div className="space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-title font-bold text-dark dark:text-white theme-transition">
                Payment Successful!
              </h1>
              <p className="text-lg sm:text-xl font-body text-dark dark:text-white theme-transition">
                Thank you for your purchase. Your payment has been processed successfully.
              </p>
            </div>

            {/* Transaction Details */}
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 space-y-4 text-left">
              <h3 className="text-lg font-title font-semibold text-dark dark:text-white theme-transition mb-4">
                Transaction Details
              </h3>

              <div className="space-y-3">
                {session.amount_total > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="font-body text-gray-600 dark:text-gray-300">Amount:</span>
                    <span className="font-body font-semibold text-dark dark:text-white">
                      {formatAmount(session.amount_total, session.currency)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span className="font-body text-gray-600 dark:text-gray-300">Status:</span>
                  <span className="font-body font-semibold text-green-600 dark:text-green-400 capitalize">
                    {session.status}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="font-body text-gray-600 dark:text-gray-300">Date:</span>
                  <span className="font-body font-semibold text-dark dark:text-white">
                    {formatDate(session.created)}
                  </span>
                </div>

                {session.customer_details.email && (
                  <div className="flex justify-between items-center">
                    <span className="font-body text-gray-600 dark:text-gray-300">Email:</span>
                    <span className="font-body font-semibold text-dark dark:text-white">
                      {session.customer_details.email}
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span className="font-body text-gray-600 dark:text-gray-300">Session ID:</span>
                  <span className="font-body font-mono text-xs text-dark dark:text-white break-all">
                    {session.id}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleReturnHome}
                className="px-8 py-4 text-lg font-body font-semibold bg-dark dark:bg-light text-white dark:text-dark rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
              >
                Return Home
              </button>
              <button
                onClick={handleViewOffer}
                className="px-8 py-4 text-lg font-body font-semibold bg-white/10 dark:bg-gray-800/30 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 rounded-full hover:bg-white/20 dark:hover:bg-gray-800/50 hover:border-dark/40 dark:hover:border-light/40 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 focus:ring-offset-2 focus:ring-offset-transparent"
              >
                View More Offers
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
