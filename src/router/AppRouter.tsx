import { Routes, Route } from "react-router-dom";
import { LandingPage } from "@/pages/LandingPage";
import { WebApp } from "@/pages/WebApp";
import { ReportPage } from "@/pages/ReportPage";
import { OfferPage } from "@/pages/OfferPage";
import { PaymentSuccessPage, PaymentCancelPage } from "@/pages/payment";
import { useLocation } from "react-router-dom";

export function AppRouter() {
  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/app" element={<WebApp />} />
      <Route path="/app/report/:id" element={<ReportPage />} />
      <Route path="/offer" element={<OfferPage />} />
      <Route path="/payment/success" element={<PaymentSuccessPage />} />
      <Route path="/payment/cancel" element={<PaymentCancelPage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
}


export function NotFoundPage() {
  const location = useLocation();
  return (
    <div className="container mx-auto p-8 text-center mt-20">
      <h1 className="text-3xl font-bold mb-4">404 — Page Not Found</h1>
      <p className="mb-6">The page you are looking for does not exist at <code>{location.pathname}</code>.</p>
      <a href="/" className="btn btn-primary">
        Go Back Home
      </a>
    </div>
  );
}