import type { Report } from "@/types/domain/report";
import type { Lead } from "@/types/domain/lead";

export interface CreateLeadPayload {
  companyName: string;
  email: string;
  fullName: string;
  phone: string;
  website: string;
  businessType: string;
  employeeCount: string;
  industry: string;
  monthlyRevenue: string;
  salesLocation: string;
  bottlenecks: string;
  timeConsumingTasks: string;
}

export interface CreateLeadResponse {
  Lead: Omit<Lead, "reports">;
  GPTResponse: Report;
}